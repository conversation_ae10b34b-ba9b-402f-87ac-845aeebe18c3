import { generatePrivate<PERSON><PERSON>, getA<PERSON>ress<PERSON><PERSON><PERSON>, Lucid, Blockfrost } from "lucid-cardano";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Check if API key is loaded
if (!process.env.BLOCKFROST_API_KEY) {
    console.error("BLOCKFROST_API_KEY not found in environment variables");
    process.exit(1);
}

// 1. Generate private key
const privateKey = generatePrivateKey();
console.log("Private Key:", privateKey);

try {
    const lucid = await Lucid.new(
        new Blockfrost("https://cardano-preprod.blockfrost.io/api/v0", process.env.BLOCKFROST_API_KEY),
        "Preprod"
    );

    lucid.selectWalletFromPrivateKey(privateKey);
    const address = await lucid.wallet.address();
    const { paymentCredential } = lucid.utils.getAddressDetails(address);
    console.log("PubKey Hash:", paymentCredential.hash);
} catch (error) {
    console.error("Error:", error.message);
    console.error("This might be due to:");
    console.error("1. Invalid BLOCKFROST_API_KEY");
    console.error("2. Network connectivity issues");
    console.error("3. Blockfrost API service issues");
}
