import dotenv from "dotenv";
import express from "express";
import mongoose from "mongoose";
import hotelRoutes from "./routes/hotelRoutes.js";

dotenv.config();

const app = express();
app.use(express.json());

app.use("/api/hotels", hotelRoutes);

mongoose.connect("mongodb://localhost/hotel-registry").then(() => {
    const port = process.env.PORT || 3000;
    app.listen(port, () => {
        console.log(`Server running on port ${port}`);
    });
});