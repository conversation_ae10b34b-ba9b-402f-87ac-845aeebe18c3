// app.js
require("dotenv").config();
const express = require("express");
const mongoose = require("mongoose");

const hotelRoutes = require("./routes/hotelRoutes");
const app = express();
app.use(express.json());

app.use("/api/hotels", hotelRoutes);
require('dotenv').config();

mongoose.connect("mongodb://localhost/hotel-registry", {
    useNewUrlParser: true,
    useUnifiedTopology: true,
}).then(() => {
    app.listen(process.env.PORT, () =>
        console.log(`Server running on port ${process.env.PORT}`)
    );
});
