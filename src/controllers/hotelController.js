import Hotel from '../models/hotelModel.js';
import { uploadToIPFS } from '../services/ipfsService.js';
import { mintVerifiedNFT } from '../services/nftMinter.js';

export const registerHotel = async (req, res) => {
    try {
        const { name, location, walletAddress } = req.body;
        const fileBuffer = req.file.buffer;
        const ipfsHash = await uploadToIPFS(fileBuffer, req.file.originalname);

        const hotel = await Hotel.create({
            name,
            location,
            walletAddress,
            licenseIPFSHash: ipfsHash,
        });

        res.status(201).json({ message: "Hotel registered", hotel });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: "Registration failed" });
    }
};

export const verifyHotel = async (req, res) => {
    const { id } = req.params;
    const hotel = await Hotel.findById(id);
    if (!hotel) return res.status(404).json({ error: "Hotel not found" });

    // Upload metadata JSON to IPFS first
    const metadataHash = await uploadToIPFS(Buffer.from(JSON.stringify({
        name: "Verified Hotel",
        location: hotel.location,
        registered_on: new Date().toISOString().slice(0, 10)
    })), `${hotel.name}-metadata.json`);

    // Make sure policyScript is defined or imported
    const txHash = await mintVerifiedNFT(
        hotel.walletAddress,
        process.env.NFT_POLICY_ID,
        policyScript, // define or import this variable
        metadataHash
    );

    hotel.isVerified = true;
    hotel.nftTxHash = txHash;
    await hotel.save();

    res.json({ message: "Hotel verified and NFT issued", txHash });
};