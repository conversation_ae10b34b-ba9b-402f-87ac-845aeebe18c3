const Hotel = require('../models/hotelModel');
const { uploadToIPFS } = require('../services/ipfsService');

const registerHotel = async (req, res) => {
    try {
        const { name, location, walletAddress } = req.body;
        const fileBuffer = req.file.buffer;
        const ipfsHash = await uploadToIPFS(fileBuffer, req.file.originalname);

        const hotel = await Hotel.create({
            name,
            location,
            walletAddress,
            licenseIPFSHash: ipfsHash,
        });

        res.status(201).json({ message: "Hotel registered", hotel });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: "Registration failed" });
    }
};

module.exports = { registerHotel };
