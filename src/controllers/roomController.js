const Room = require("../models/roomModel.js");

exports.createRoom = async (req, res) => {
    try {
        const room = await Room.create(req.body);
        res.status(201).json(room);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};

exports.getRoomsByHotel = async (req, res) => {
    try {
        const rooms = await Room.find({ hotelId: req.params.hotelId });
        res.json(rooms);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};
