// ipfs upload service

const axios = require('axios');
const FormData = require('form-data');

const uploadToIPFS = async (fileBuffer, fileName) => {
    const form = new FormData();
    form.append("file", fileBuffer, fileName);

    const res = await axios.post("https://ipfs.infura.io:5001/api/v0/add", form, {
        headers: {
            ...form.getHeaders(),
            Authorization: `Basic ${Buffer.from(`${process.env.IPFS_PROJECT_ID}:${process.env.IPFS_API_KEY}`).toString('base64')}`
        },
    });

    return res.data.Hash;
};

module.exports = { uploadToIPFS };
