# Hotel Registry Service

## Functionalities
1. Register hotels with name, location, license document (stored on IPFS), wallet address, and optional metadata.

2. Upload the license document to IPFS.

3. Store hotel data off-chain in PostgreSQL (or MongoDB temporarily).

4. Create a placeholder for DID or NFT minting (smart contract call).

5. Fetch hotel info via REST endpoints.

6. Prepare a hash payload for future smart contract calls.


## API endpoints

| Method | Route                    | Function                      |
| ------ | ------------------------ | ----------------------------- |
| `POST` | `/api/hotels/register`   | Register a new hotel          |
| `GET`  | `/api/hotels/:id`        | Get hotel details             |
| `GET`  | `/api/hotels`            | List all hotels               |
| `POST` | `/api/hotels/verify/:id` | Admin verification (optional) |

## What is done by the verify and mint the NFT smart contract

1. Connects to Cardano testnet using Blockfrost and Lucid.
2. Loads the admin wallet (from a private key in environment variables) to sign the minting transaction.
3. Builds the NFT asset unit (policyId + asset name in hex).
4. Prepares NFT metadata (name, image, description, registration date).
5. Creates a transaction that:
    - Mints 1 NFT using the provided policy and a Unit redeemer.
    - Pays the NFT to the hotel’s wallet address.
    - Attaches the metadata under the 721 standard.
6. Signs and submits the transaction to the blockchain.
7. Returns the transaction hash as confirmation
