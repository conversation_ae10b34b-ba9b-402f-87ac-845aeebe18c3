# HotelRegistry-Service

## Functionalities
1. Register hotels with name, location, license document (stored on IPFS), wallet address, and optional metadata.

2. Upload the license document to IPFS.

3. Store hotel data off-chain in PostgreSQL (or MongoDB temporarily).

4. Create a placeholder for DID or NFT minting (smart contract call).

5. Fetch hotel info via REST endpoints.

6. Prepare a hash payload for future smart contract calls.