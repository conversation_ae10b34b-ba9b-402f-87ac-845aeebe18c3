use aiken/primitive/bytearray
use cardano/address.{C<PERSON><PERSON>, Script, VerificationKey}

pub fn compare(left: Credential, right: Credential) -> Ordering {
  when left is {
    Script(left) ->
      when right is {
        <PERSON>ript(right) -> bytearray.compare(left, right)
        _ -> Less
      }
    VerificationKey(left) ->
      when right is {
        Script(_) -> Greater
        VerificationKey(right) -> bytearray.compare(left, right)
      }
  }
}

test compare_matrix() {
  and {
    (compare(<PERSON><PERSON><PERSON>(""), <PERSON>ript("")) == Equal)?,
    (compare(VerificationKey(""), VerificationKey("")) == Equal)?,
    (compare(<PERSON>rip<PERSON>(""), VerificationKey("")) == Less)?,
    (compare(VerificationKey(""), Script("")) == Greater)?,
    (compare(<PERSON><PERSON><PERSON>("01"), <PERSON>rip<PERSON>("02")) == Less)?,
    (compare(<PERSON><PERSON><PERSON>("02"), <PERSON><PERSON><PERSON>("01")) == Greater)?,
    (compare(Verification<PERSON><PERSON>("01"), Verification<PERSON>ey("02")) == Less)?,
    (compare(Verification<PERSON>ey("02"), VerificationKey("01")) == Greater)?,
  }
}
