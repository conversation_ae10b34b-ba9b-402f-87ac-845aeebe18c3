{"preamble": {"title": "ranaweerahk/verified_nft_policy", "description": "Aiken contracts for project 'ranaweerahk/verified_nft_policy'", "version": "0.0.0", "plutusVersion": "v3", "compiler": {"name": "Aiken", "version": "v1.1.17+c3a7fba"}, "license": "Apache-2.0"}, "validators": [{"title": "placeholder.placeholder.mint", "redeemer": {"title": "_redeemer", "schema": {"$ref": "#/definitions/Data"}}, "compiledCode": "58d001010029800aba2aba1aab9eaab9dab9a48888966002646465300130053754003300700398038012444b30013370e9000001c4c98dd7180518049baa0048acc004cdc3a400400713233226300b001300b300c0013009375400915980099b874801000e264c601460126ea80122b30013370e9003001c4c8cc898dd698058009805980600098049baa0048acc004cdc3a40100071326300a3009375400913233226375a60160026016601800260126ea8011007200e401c80390070c018c01c004c018004c00cdd5003452689b2b20021", "hash": "f2388d136606a27c4a531d0040c3e12e07eb95cd5011793c160707dc"}, {"title": "placeholder.placeholder.spend", "datum": {"title": "_datum", "schema": {"$ref": "#/definitions/Data"}}, "redeemer": {"title": "_redeemer", "schema": {"$ref": "#/definitions/Data"}}, "compiledCode": "58d001010029800aba2aba1aab9eaab9dab9a48888966002646465300130053754003300700398038012444b30013370e9000001c4c98dd7180518049baa0048acc004cdc3a400400713233226300b001300b300c0013009375400915980099b874801000e264c601460126ea80122b30013370e9003001c4c8cc898dd698058009805980600098049baa0048acc004cdc3a40100071326300a3009375400913233226375a60160026016601800260126ea8011007200e401c80390070c018c01c004c018004c00cdd5003452689b2b20021", "hash": "f2388d136606a27c4a531d0040c3e12e07eb95cd5011793c160707dc"}, {"title": "placeholder.placeholder.withdraw", "redeemer": {"title": "_redeemer", "schema": {"$ref": "#/definitions/Data"}}, "compiledCode": "58d001010029800aba2aba1aab9eaab9dab9a48888966002646465300130053754003300700398038012444b30013370e9000001c4c98dd7180518049baa0048acc004cdc3a400400713233226300b001300b300c0013009375400915980099b874801000e264c601460126ea80122b30013370e9003001c4c8cc898dd698058009805980600098049baa0048acc004cdc3a40100071326300a3009375400913233226375a60160026016601800260126ea8011007200e401c80390070c018c01c004c018004c00cdd5003452689b2b20021", "hash": "f2388d136606a27c4a531d0040c3e12e07eb95cd5011793c160707dc"}, {"title": "placeholder.placeholder.publish", "redeemer": {"title": "_redeemer", "schema": {"$ref": "#/definitions/Data"}}, "compiledCode": "58d001010029800aba2aba1aab9eaab9dab9a48888966002646465300130053754003300700398038012444b30013370e9000001c4c98dd7180518049baa0048acc004cdc3a400400713233226300b001300b300c0013009375400915980099b874801000e264c601460126ea80122b30013370e9003001c4c8cc898dd698058009805980600098049baa0048acc004cdc3a40100071326300a3009375400913233226375a60160026016601800260126ea8011007200e401c80390070c018c01c004c018004c00cdd5003452689b2b20021", "hash": "f2388d136606a27c4a531d0040c3e12e07eb95cd5011793c160707dc"}, {"title": "placeholder.placeholder.vote", "redeemer": {"title": "_redeemer", "schema": {"$ref": "#/definitions/Data"}}, "compiledCode": "58d001010029800aba2aba1aab9eaab9dab9a48888966002646465300130053754003300700398038012444b30013370e9000001c4c98dd7180518049baa0048acc004cdc3a400400713233226300b001300b300c0013009375400915980099b874801000e264c601460126ea80122b30013370e9003001c4c8cc898dd698058009805980600098049baa0048acc004cdc3a40100071326300a3009375400913233226375a60160026016601800260126ea8011007200e401c80390070c018c01c004c018004c00cdd5003452689b2b20021", "hash": "f2388d136606a27c4a531d0040c3e12e07eb95cd5011793c160707dc"}, {"title": "placeholder.placeholder.propose", "redeemer": {"title": "_redeemer", "schema": {"$ref": "#/definitions/Data"}}, "compiledCode": "58d001010029800aba2aba1aab9eaab9dab9a48888966002646465300130053754003300700398038012444b30013370e9000001c4c98dd7180518049baa0048acc004cdc3a400400713233226300b001300b300c0013009375400915980099b874801000e264c601460126ea80122b30013370e9003001c4c8cc898dd698058009805980600098049baa0048acc004cdc3a40100071326300a3009375400913233226375a60160026016601800260126ea8011007200e401c80390070c018c01c004c018004c00cdd5003452689b2b20021", "hash": "f2388d136606a27c4a531d0040c3e12e07eb95cd5011793c160707dc"}, {"title": "placeholder.placeholder.else", "redeemer": {"schema": {}}, "compiledCode": "58d001010029800aba2aba1aab9eaab9dab9a48888966002646465300130053754003300700398038012444b30013370e9000001c4c98dd7180518049baa0048acc004cdc3a400400713233226300b001300b300c0013009375400915980099b874801000e264c601460126ea80122b30013370e9003001c4c8cc898dd698058009805980600098049baa0048acc004cdc3a40100071326300a3009375400913233226375a60160026016601800260126ea8011007200e401c80390070c018c01c004c018004c00cdd5003452689b2b20021", "hash": "f2388d136606a27c4a531d0040c3e12e07eb95cd5011793c160707dc"}], "definitions": {"Data": {"title": "Data", "description": "Any Plutus data."}}}